@extends('theme.layout.master')

@section('title', 'Comments Management')

@section('content')
<div class="app-title">
  <div>
    <h1><i class="bi bi-chat-dots"></i> Comments Management</h1>
    <p>Manage all comments from your blog posts</p>
  </div>
</div>

<!-- Success/Error Messages -->
@if(session('success'))
  <div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="bi bi-check-circle"></i> {{ session('success') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
@endif

@if(session('error'))
  <div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="bi bi-exclamation-triangle"></i> {{ session('error') }}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
@endif

@if($errors->any())
  <div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="bi bi-exclamation-triangle"></i>
    <ul class="mb-0">
      @foreach($errors->all() as $error)
        <li>{{ $error }}</li>
      @endforeach
    </ul>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
@endif

<div class="app-title">
  <div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door"></i></li>
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
    <li class="breadcrumb-item active">Comments</li>
  </ul>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
  <div class="col-md-3">
    <div class="widget-small primary coloured-icon">
      <i class="icon bi bi-chat-dots fs-1"></i>
      <div class="info">
        <h4>Total Comments</h4>
        <p><b>{{ $statistics['total'] }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small info coloured-icon">
      <i class="icon bi bi-check-circle fs-1"></i>
      <div class="info">
        <h4>Approved</h4>
        <p><b>{{ $statistics['approved'] }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small warning coloured-icon">
      <i class="icon bi bi-clock fs-1"></i>
      <div class="info">
        <h4>Pending</h4>
        <p><b>{{ $statistics['pending'] }}</b></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="widget-small danger coloured-icon">
      <i class="icon bi bi-calendar-date fs-1"></i>
      <div class="info">
        <h4>This Month</h4>
        <p><b>{{ $statistics['this_month'] }}</b></p>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="tile">
      <div class="tile-body">
        <!-- Search and Filter Form -->
        <form method="GET" action="{{ route('comments.index') }}" class="mb-4">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="search">Search Comments</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request('search') }}" placeholder="Search by name, email, comment, or post title...">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label for="status">Status</label>
                <select class="form-control" id="status" name="status">
                  <option value="">All Comments</option>
                  <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Approved</option>
                  <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Pending</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>&nbsp;</label>
                <div>
                  <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i> Search
                  </button>
                  <a href="{{ route('comments.index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-clockwise"></i> Reset
                  </a>
                </div>
              </div>
            </div>
          </div>
        </form>

        <!-- Comments List -->
        <div class="comments-container">
          @forelse($comments->where('parent_id', null) as $comment)
            <div class="comment-card mb-4 {{ $comment->is_approved ? '' : 'pending-comment' }}" id="comment-{{ $comment->id }}">
              <div class="card">
                <div class="card-body">
                  <!-- Comment Header -->
                  <div class="d-flex justify-content-between align-items-start mb-3">
                    <div class="comment-meta">
                      <div class="d-flex align-items-center mb-2">
                        <div class="avatar-circle me-3">
                          {{ strtoupper(substr($comment->name, 0, 1)) }}
                        </div>
                        <div>
                          <h6 class="mb-0 fw-bold">{{ $comment->name }}</h6>
                          <small class="text-muted">{{ $comment->email }}</small>
                        </div>
                      </div>
                      <div class="comment-info">
                        <small class="text-muted">
                          <i class="bi bi-calendar3"></i> {{ $comment->created_at->format('M d, Y h:i A') }}
                          <span class="mx-2">•</span>
                          <i class="bi bi-file-text"></i>
                          <a href="{{ route('post-detail', $comment->post->slug) }}" target="_blank" class="text-decoration-none">
                            {{ Str::limit($comment->post->title, 40) }}
                          </a>
                        </small>
                      </div>
                    </div>
                    <div class="comment-status">
                      @if($comment->is_approved)
                        <span class="badge bg-success">
                          <i class="bi bi-check-circle"></i> Approved
                        </span>
                      @else
                        <span class="badge bg-warning">
                          <i class="bi bi-clock"></i> Pending
                        </span>
                      @endif
                    </div>
                  </div>

                  <!-- Comment Content -->
                  <div class="comment-content mb-3">
                    <p class="mb-0">{{ $comment->comment }}</p>
                  </div>

                  <!-- Comment Actions -->
                  <div class="comment-actions d-flex justify-content-between align-items-center">
                    <div class="action-buttons">
                      @if(!$comment->is_approved)
                        <form method="POST" action="{{ route('comments.approve', $comment) }}" class="d-inline">
                          @csrf
                          @method('PATCH')
                          <button type="submit" class="btn btn-success btn-sm">
                            <i class="bi bi-check-lg"></i> Approve
                          </button>
                        </form>
                      @else
                        <form method="POST" action="{{ route('comments.reject', $comment) }}" class="d-inline">
                          @csrf
                          @method('PATCH')
                          <button type="submit" class="btn btn-warning btn-sm">
                            <i class="bi bi-x-lg"></i> Reject
                          </button>
                        </form>
                      @endif

                      <button type="button" class="btn btn-primary btn-sm" onclick="toggleReplyForm({{ $comment->id }})">
                        <i class="bi bi-reply"></i> Reply
                      </button>

                      <form method="POST" action="{{ route('comments.destroy', $comment) }}" class="d-inline"
                            onsubmit="return confirm('Are you sure you want to delete this comment and all its replies?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger btn-sm">
                          <i class="bi bi-trash"></i> Delete
                        </button>
                      </form>
                    </div>

                    @if($comment->allReplies->count() > 0)
                      <small class="text-muted">
                        <i class="bi bi-chat-dots"></i> {{ $comment->allReplies->count() }}
                        {{ $comment->allReplies->count() == 1 ? 'reply' : 'replies' }}
                      </small>
                    @endif
                  </div>

                  <!-- Reply Form -->
                  <div id="reply-form-{{ $comment->id }}" class="reply-form mt-3" style="display: none;">
                    <form method="POST" action="{{ route('comments.reply', $comment) }}" onsubmit="console.log('Reply form submitted for comment {{ $comment->id }}');">
                      @csrf
                      <div class="form-group mb-3">
                        <label for="reply_content_{{ $comment->id }}" class="form-label">Your Reply</label>
                        <textarea class="form-control @error('reply_content') is-invalid @enderror"
                                  id="reply_content_{{ $comment->id }}" name="reply_content"
                                  rows="3" placeholder="Write your reply..." required>{{ old('reply_content') }}</textarea>
                        @error('reply_content')
                          <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                      </div>
                      <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary btn-sm" id="reply-btn-{{ $comment->id }}">
                          <i class="bi bi-send"></i> Post Reply
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="toggleReplyForm({{ $comment->id }})">
                          Cancel
                        </button>
                      </div>

                      <!-- Success/Error Messages -->
                      <div id="reply-message-{{ $comment->id }}" class="mt-2" style="display: none;"></div>
                    </form>
                  </div>

                  <!-- Replies -->
                  @if($comment->allReplies->count() > 0)
                    <div class="replies-section mt-4">
                      <h6 class="replies-title">
                        <i class="bi bi-arrow-return-right"></i> Replies ({{ $comment->allReplies->count() }})
                      </h6>
                      @foreach($comment->allReplies as $reply)
                        <div class="reply-card {{ $reply->is_approved ? '' : 'pending-reply' }}">
                          <div class="d-flex">
                            <div class="avatar-circle-small me-3">
                              {{ strtoupper(substr($reply->name, 0, 1)) }}
                            </div>
                            <div class="flex-grow-1">
                              <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                  <h6 class="mb-0 fw-bold">{{ $reply->name }}</h6>
                                  <small class="text-muted">{{ $reply->created_at->format('M d, Y h:i A') }}</small>
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                  @if($reply->is_approved)
                                    <span class="badge bg-success badge-sm">
                                      <i class="bi bi-check-circle"></i>
                                    </span>
                                  @else
                                    <span class="badge bg-warning badge-sm">
                                      <i class="bi bi-clock"></i>
                                    </span>
                                  @endif

                                  @if(!$reply->is_approved)
                                    <form method="POST" action="{{ route('comments.approve', $reply) }}" class="d-inline">
                                      @csrf
                                      @method('PATCH')
                                      <button type="submit" class="btn btn-success btn-xs" title="Approve Reply">
                                        <i class="bi bi-check-lg"></i>
                                      </button>
                                    </form>
                                  @endif

                                  <form method="POST" action="{{ route('comments.destroy', $reply) }}" class="d-inline"
                                        onsubmit="return confirm('Are you sure you want to delete this reply?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger btn-xs" title="Delete Reply">
                                      <i class="bi bi-trash"></i>
                                    </button>
                                  </form>
                                </div>
                              </div>
                              <p class="mb-0">{{ $reply->comment }}</p>
                            </div>
                          </div>
                        </div>
                      @endforeach
                    </div>
                  @endif
                </div>
              </div>
            </div>
          @empty
            <div class="text-center py-5">
              <i class="bi bi-chat-dots display-1 text-muted"></i>
              <h4 class="text-muted mt-3">No comments found</h4>
              <p class="text-muted">Comments will appear here once users start commenting on your posts.</p>
            </div>
          @endforelse
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-center">
          {{ $comments->appends(request()->query())->links() }}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleFullComment(commentId) {
  const fullComment = document.getElementById('full-comment-' + commentId);
  if (fullComment.style.display === 'none') {
    fullComment.style.display = 'block';
  } else {
    fullComment.style.display = 'none';
  }
}
</script>

<style>
/* Comments Container Styles */
.comments-container {
  max-width: 100%;
}

.comment-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.comment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.pending-comment {
  background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
}

/* Avatar Styles */
.avatar-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 18px;
  background: linear-gradient(135deg, #007bff, #0056b3);
}

.avatar-circle-small {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 14px;
  background: linear-gradient(135deg, #17a2b8, #138496);
}

/* Comment Content Styles */
.comment-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #007bff;
}

.comment-meta {
  font-size: 0.875rem;
}

/* Reply Styles */
.replies-section {
  border-left: 3px solid #dee2e6;
  padding-left: 20px;
  margin-left: 20px;
}

.reply-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 3px solid #17a2b8;
}

.pending-reply {
  background: linear-gradient(135deg, #fff3cd 0%, #f8f9fa 100%);
  border-left-color: #ffc107;
}

.reply-form {
  background: #e9ecef;
  border-radius: 8px;
  padding: 20px;
  border: 2px dashed #dee2e6;
}

/* Button Styles */
.btn-xs {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
}

.action-buttons .btn {
  margin-right: 5px;
}

/* Badge Styles */
.badge-sm {
  font-size: 0.75em;
}

/* Statistics Cards */
.widget-small {
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.widget-small:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
  .comment-card {
    margin-bottom: 20px;
  }

  .replies-section {
    margin-left: 10px;
    padding-left: 15px;
  }

  .action-buttons .btn {
    margin-bottom: 5px;
  }
}

/* Animation for new replies */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.reply-form {
  animation: slideIn 0.3s ease;
}
</style>

<script>
// Toggle reply form visibility
function toggleReplyForm(commentId) {
  const replyForm = document.getElementById('reply-form-' + commentId);
  const isVisible = replyForm.style.display !== 'none';

  // Hide all other reply forms first
  document.querySelectorAll('.reply-form').forEach(form => {
    form.style.display = 'none';
  });

  // Toggle current form
  if (!isVisible) {
    replyForm.style.display = 'block';
    // Focus on textarea
    const textarea = replyForm.querySelector('textarea');
    if (textarea) {
      textarea.focus();
    }
    // Clear any previous messages
    const messageDiv = document.getElementById('reply-message-' + commentId);
    if (messageDiv) {
      messageDiv.style.display = 'none';
      messageDiv.innerHTML = '';
    }
  }
}

// Enhanced form submission handling
document.addEventListener('DOMContentLoaded', function() {
  // Handle all reply forms
  document.querySelectorAll('form[action*="reply"]').forEach(form => {
    form.addEventListener('submit', function(e) {
      const submitBtn = form.querySelector('button[type="submit"]');
      const textarea = form.querySelector('textarea[name="reply_content"]');

      // Basic validation
      if (!textarea.value.trim() || textarea.value.trim().length < 10) {
        e.preventDefault();
        alert('Reply must be at least 10 characters long.');
        return false;
      }

      // Disable submit button to prevent double submission
      if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Posting...';
      }

      console.log('Reply form submitted:', {
        action: form.action,
        content: textarea.value,
        length: textarea.value.length
      });
    });
  });
});

// Auto-resize textareas
document.addEventListener('DOMContentLoaded', function() {
  const textareas = document.querySelectorAll('textarea');
  textareas.forEach(textarea => {
    textarea.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = (this.scrollHeight) + 'px';
    });
  });

  // Add smooth scrolling to comment actions
  document.querySelectorAll('.comment-actions form').forEach(form => {
    form.addEventListener('submit', function(e) {
      // Add loading state to buttons
      const button = this.querySelector('button[type="submit"]');
      if (button) {
        button.disabled = true;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';

        // Re-enable after 3 seconds (in case of errors)
        setTimeout(() => {
          button.disabled = false;
          button.innerHTML = originalText;
        }, 3000);
      }
    });
  });
});

// Search functionality enhancement
document.addEventListener('DOMContentLoaded', function() {
  const searchInput = document.getElementById('search');
  if (searchInput) {
    let searchTimeout;
    searchInput.addEventListener('input', function() {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        // Auto-submit search after 1 second of no typing
        if (this.value.length >= 3 || this.value.length === 0) {
          this.closest('form').submit();
        }
      }, 1000);
    });
  }
});
</script>

@endsection
