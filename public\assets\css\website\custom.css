/* Website Custom Styles */

/* Image height fixes */
.main-banner .owl-carousel .item img {
  height: 427px !important;
  object-fit: cover;
  width: 100%;
}

.blog-post .blog-thumb img {
  height: 250px !important;
  object-fit: cover;
  width: 100%;
}

.sidebar-item.recent-posts .content ul li img {
  height: 60px !important;
  width: 60px !important;
  object-fit: cover;
}

/* Filter tags styling */
.filter-tags {
  margin-top: 10px;
}

.filter-tag {
  display: inline-block;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 15px;
  padding: 5px 12px;
  margin: 3px;
  font-size: 12px;
  color: #495057;
}

.filter-tag .remove-filter {
  margin-left: 8px;
  color: #dc3545;
  text-decoration: none;
  font-weight: bold;
}

.filter-tag .remove-filter:hover {
  color: #c82333;
}

.clear-all-filters {
  display: inline-block;
  background: #dc3545;
  color: white !important;
  border-radius: 15px;
  padding: 5px 12px;
  margin: 3px;
  font-size: 12px;
  text-decoration: none;
}

.clear-all-filters:hover {
  background: #c82333;
  color: white !important;
}

/* Active filter links */
.sidebar-item a.active {
  color: #f48840 !important;
  font-weight: bold;
}

/* Post content styling */
.post-excerpt {
  background: #f8f9fa;
  border-left: 4px solid #f48840;
  padding: 15px;
  margin: 20px 0;
  border-radius: 0 5px 5px 0;
}

.post-content {
  line-height: 1.8;
  font-size: 1.1rem;
  margin: 20px 0;
}

.post-content p {
  margin-bottom: 1.5rem;
}

/* Related posts styling */
.related-posts .blog-post {
  transition: transform 0.3s ease;
}

.related-posts .blog-post:hover {
  transform: translateY(-5px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-banner .owl-carousel .item img {
    height: 300px !important;
  }
  
  .blog-post .blog-thumb img {
    height: 200px !important;
  }
  
  .filter-tag {
    font-size: 11px;
    padding: 4px 10px;
  }
}

/* Search form enhancements */
.sidebar-item.search form {
  position: relative;
}

.sidebar-item.search .searchText {
  padding-right: 40px;
}

.sidebar-item.search form::after {
  content: '\f002';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  pointer-events: none;
}

/* Blog post hover effects */
.blog-post {
  transition: all 0.3s ease;
}

/* Enhanced Pagination Styling */
.pagination-wrapper {
  text-align: center;
  margin: 50px 0;
  padding: 30px 0;
  background: rgba(248, 249, 250, 0.5);
  border-radius: 15px;
}

.pagination-info {
  margin-bottom: 25px;
}

.pagination-info p {
  font-size: 15px;
  color: #555;
  margin: 0;
  font-weight: 500;
  letter-spacing: 0.3px;
}

ul.page-numbers {
  display: inline-flex;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: 8px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

ul.page-numbers li {
  margin: 0;
}

ul.page-numbers li a,
ul.page-numbers li span {
  width: 45px;
  height: 45px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  color: #7a7a7a;
  border: 2px solid #eee;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #fff;
}

ul.page-numbers li.active a {
  background: linear-gradient(135deg, #f48840 0%, #fb9857 100%);
  border-color: #f48840;
  color: #fff;
  box-shadow: 0 4px 15px rgba(244, 136, 64, 0.3);
  transform: translateY(-2px);
}

ul.page-numbers li a:hover {
  color: #fff;
  background: linear-gradient(135deg, #f48840 0%, #fb9857 100%);
  border-color: #f48840;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(244, 136, 64, 0.3);
}

ul.page-numbers li.disabled span {
  color: #ccc;
  border-color: #f0f0f0;
  background: #f8f9fa;
  cursor: not-allowed;
}

/* Pagination responsive design */
@media (max-width: 768px) {
  ul.page-numbers li a,
  ul.page-numbers li span {
    width: 40px;
    height: 40px;
    font-size: 13px;
  }

  .pagination-wrapper {
    margin: 30px 0;
  }
}

.blog-post:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Tag styling in posts */
.post-tags li a {
  background: #f48840;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-decoration: none;
  margin-right: 5px;
}

.post-tags li a:hover {
  background: #e67e22;
  color: white;
}

/* Social share buttons */
.post-share li a {
  color: #666;
  transition: color 0.3s ease;
}

.post-share li a:hover {
  color: #f48840;
}

/* Pagination styling */
.pagination {
  justify-content: center;
  margin-top: 30px;
}

.pagination .page-link {
  color: #f48840;
  border-color: #f48840;
}

.pagination .page-item.active .page-link {
  background-color: #f48840;
  border-color: #f48840;
}

.pagination .page-link:hover {
  color: #e67e22;
  border-color: #e67e22;
}

/* Loading animations */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Custom scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #f48840;
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #e67e22;
}

/* Notification styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
}

.notification.show {
  opacity: 1;
  transform: translateX(0);
}

.notification-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-success .notification-content {
  border-left: 4px solid #28a745;
}

.notification-error .notification-content {
  border-left: 4px solid #dc3545;
}

.notification-info .notification-content {
  border-left: 4px solid #17a2b8;
}

.notification-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #999;
  margin-left: 10px;
}

/* Reading progress bar */
.reading-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: rgba(244, 136, 64, 0.2);
  z-index: 9999;
}

.reading-progress-bar {
  height: 100%;
  background: #f48840;
  width: 0%;
  transition: width 0.3s ease;
}

/* Back to top button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: #f48840;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background: #e67e22;
  transform: translateY(-2px);
}

/* Lazy loading images */
img.lazy {
  opacity: 0;
  transition: opacity 0.3s;
}

img.lazy.loaded {
  opacity: 1;
}

/* Animation classes */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.animated {
  animation-duration: 0.3s;
  animation-fill-mode: both;
}

/* Mobile optimizations */
@media (max-width: 480px) {
  .notification {
    left: 10px;
    right: 10px;
    max-width: none;
  }

  .back-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
  }

  .reading-progress {
    height: 2px;
  }
}
