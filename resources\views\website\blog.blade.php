@extends('website.layouts.master')
@section('content')
<!-- Page Content -->
    <!-- Banner Starts Here -->
    <div class="heading-page header-text">
      <section class="page-heading">
        <div class="container">
          <div class="row">
            <div class="col-lg-12">
              <div class="text-content">
                <h4>Recent Posts</h4>
                <h2>Our Recent Blog Entries</h2>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    
    <!-- Banner Ends Here -->

    <section class="call-to-action">
      <div class="container">
        <div class="row">
          <div class="col-lg-12">
            <div class="main-content">
              <div class="row">
                <div class="col-lg-8">
                  <span>Stand Blog HTML5 Template</span>
                  <h4>Creative HTML Template For Bloggers!</h4>
                </div>
                <div class="col-lg-4">
                  <div class="main-button">
                    <a href="https://templatemo.com/tm-551-stand-blog" target="_parent">Download Now!</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <section class="blog-posts grid-system">
      <div class="container">
        <div class="row">
          <div class="col-lg-8">
            <div class="all-blog-posts">
              <div class="row">
                @forelse($posts as $post)
                <div class="col-lg-6">
                  <div class="blog-post">
                    <div class="blog-thumb">
                      @if($post->featured_image)
                        <img src="{{ asset('storage/' . $post->featured_image) }}" alt="{{ $post->title }}">
                      @else
                        <img src="{{ asset('assets/images/blog-thumb-0' . (($loop->index % 6) + 1) . '.jpg') }}" alt="{{ $post->title }}">
                      @endif
                    </div>
                    <div class="down-content">
                      <span>{{ $post->category->name }}</span>
                      <a href="{{ route('post-detail', $post->slug) }}"><h4>{{ Str::limit($post->title, 30) }}</h4></a>
                      <ul class="post-info">
                        <li><a href="#">{{ $post->user->name }}</a></li>
                        <li><a href="#">{{ $post->published_at ? $post->published_at->format('M d, Y') : $post->created_at->format('M d, Y') }}</a></li>
                        <li><a href="#">{{ $post->views_count }} Views</a></li>
                      </ul>
                      <p>{{ $post->excerpt ?: Str::limit(strip_tags($post->content), 100) }}</p>
                      <div class="post-options">
                        <div class="row">
                          <div class="col-lg-12">
                            <ul class="post-tags">
                              <li><i class="fa fa-tags"></i></li>
                              @foreach($post->tags->take(2) as $tag)
                                <li><a href="{{ route('blog', ['tag' => $tag->slug]) }}">{{ $tag->name }}</a>@if(!$loop->last),@endif</li>
                              @endforeach
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                @empty
                <div class="col-lg-12">
                  <div class="text-center py-5">
                    <i class="fa fa-file-text-o fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">No Posts Found</h4>
                    <p class="text-muted">There are no blog posts available at the moment.</p>
                  </div>
                </div>
                @endforelse

                <!-- Laravel Pagination -->
                @if($posts->hasPages())
                <div class="col-lg-12">
                  <div class="d-flex justify-content-center">
                    {{ $posts->appends(request()->query())->links() }}
                  </div>
                </div>
                @endif
              </div>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="sidebar">
              <div class="row">
                <div class="col-lg-12">
                  <div class="sidebar-item search">
                    <div class="website-search-container">
                      <input type="text" class="searchText website-search" placeholder="Search posts..." autocomplete="off">
                    </div>
                    <form id="search_form" name="gs" method="GET" action="{{ route('blog') }}">
                      <input type="text" name="search" class="searchText" placeholder="Search posts..."
                             value="{{ request('search') }}" autocomplete="on">
                      <!-- Preserve other filters -->
                      @if(request('category'))
                        <input type="hidden" name="category" value="{{ request('category') }}">
                      @endif
                      @if(request('tag'))
                        <input type="hidden" name="tag" value="{{ request('tag') }}">
                      @endif
                    </form>
                  </div>
                </div>

                <!-- Current Filters -->
                @if(request('search') || request('category') || request('tag'))
                <div class="col-lg-12">
                  <div class="sidebar-item">
                    <div class="sidebar-heading">
                      <h2>Active Filters</h2>
                    </div>
                    <div class="content">
                      <div class="filter-tags">
                        @if(request('search'))
                          <span class="filter-tag">
                            Search: "{{ request('search') }}"
                            <a href="{{ route('blog', array_filter(request()->except('search'))) }}" class="remove-filter">×</a>
                          </span>
                        @endif
                        @if(request('category'))
                          <span class="filter-tag">
                            Category: {{ ucfirst(request('category')) }}
                            <a href="{{ route('blog', array_filter(request()->except('category'))) }}" class="remove-filter">×</a>
                          </span>
                        @endif
                        @if(request('tag'))
                          <span class="filter-tag">
                            Tag: {{ ucfirst(request('tag')) }}
                            <a href="{{ route('blog', array_filter(request()->except('tag'))) }}" class="remove-filter">×</a>
                          </span>
                        @endif
                        <a href="{{ route('blog') }}" class="clear-all-filters">Clear All</a>
                      </div>
                    </div>
                  </div>
                </div>
                @endif

                <div class="col-lg-12">
                  <div class="sidebar-item recent-posts">
                    <div class="sidebar-heading">
                      <h2>Recent Posts</h2>
                    </div>
                    <div class="content">
                      <ul>
                        @forelse($recent_posts as $recent_post)
                        <li><a href="{{ route('post-detail', $recent_post->slug) }}">
                          <h5>{{ Str::limit($recent_post->title, 50) }}</h5>
                          <span>{{ $recent_post->published_at ? $recent_post->published_at->format('M d, Y') : $recent_post->created_at->format('M d, Y') }}</span>
                        </a></li>
                        @empty
                        <li><a href="#">
                          <h5>No recent posts available</h5>
                          <span>{{ date('M d, Y') }}</span>
                        </a></li>
                        @endforelse
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item categories">
                    <div class="sidebar-heading">
                      <h2>Categories</h2>
                    </div>
                    <div class="content">
                      <ul>
                        @forelse($categories as $category)
                        <li>
                          <a href="{{ route('blog', ['category' => $category->slug]) }}"
                             class="{{ request('category') == $category->slug ? 'active' : '' }}">
                            - {{ $category->name }} ({{ $category->posts_count }})
                          </a>
                        </li>
                        @empty
                        <li><a href="#">- No categories available</a></li>
                        @endforelse
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item tags">
                    <div class="sidebar-heading">
                      <h2>Tag Clouds</h2>
                    </div>
                    <div class="content">
                      <ul>
                        @forelse($tags as $tag)
                        <li>
                          <a href="{{ route('blog', ['tag' => $tag->slug]) }}"
                             class="{{ request('tag') == $tag->slug ? 'active' : '' }}">
                            {{ $tag->name }}
                          </a>
                        </li>
                        @empty
                        <li><a href="#">No tags available</a></li>
                        @endforelse
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
@endsection