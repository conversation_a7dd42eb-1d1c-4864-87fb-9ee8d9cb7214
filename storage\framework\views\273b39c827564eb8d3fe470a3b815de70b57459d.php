
<?php $__env->startSection('content'); ?>
<!-- Page Content -->
    <!-- Banner Starts Here -->
    <div class="heading-page header-text">
      <section class="page-heading">
        <div class="container">
          <div class="row">
            <div class="col-lg-12">
              <div class="text-content">
                <h4>Recent Posts</h4>
                <h2>Our Recent Blog Entries</h2>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    
    <!-- Banner Ends Here -->

    <section class="call-to-action">
      <div class="container">
        <div class="row">
          <div class="col-lg-12">
            <div class="main-content">
              <div class="row">
                <div class="col-lg-8">
                  <span>Stand Blog HTML5 Template</span>
                  <h4>Creative HTML Template For Bloggers!</h4>
                </div>
                <div class="col-lg-4">
                  <div class="main-button">
                    <a href="https://templatemo.com/tm-551-stand-blog" target="_parent">Download Now!</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <section class="blog-posts grid-system">
      <div class="container">
        <div class="row">
          <div class="col-lg-8">
            <div class="all-blog-posts">
              <div class="row">
                <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-lg-6">
                  <div class="blog-post">
                    <div class="blog-thumb">
                      <?php if($post->featured_image): ?>
                        <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" alt="<?php echo e($post->title); ?>">
                      <?php else: ?>
                        <img src="<?php echo e(asset('assets/images/blog-thumb-0' . (($loop->index % 6) + 1) . '.jpg')); ?>" alt="<?php echo e($post->title); ?>">
                      <?php endif; ?>
                    </div>
                    <div class="down-content">
                      <span><?php echo e($post->category->name); ?></span>
                      <a href="<?php echo e(route('post-detail', $post->slug)); ?>"><h4><?php echo e(Str::limit($post->title, 30)); ?></h4></a>
                      <ul class="post-info">
                        <li><a href="#"><?php echo e($post->user->name); ?></a></li>
                        <li><a href="#"><?php echo e($post->published_at ? $post->published_at->format('M d, Y') : $post->created_at->format('M d, Y')); ?></a></li>
                        <li><a href="#"><?php echo e($post->views_count); ?> Views</a></li>
                      </ul>
                      <p><?php echo e($post->excerpt ?: Str::limit(strip_tags($post->content), 100)); ?></p>
                      <div class="post-options">
                        <div class="row">
                          <div class="col-lg-12">
                            <ul class="post-tags">
                              <li><i class="fa fa-tags"></i></li>
                              <?php $__currentLoopData = $post->tags->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><a href="<?php echo e(route('blog', ['tag' => $tag->slug])); ?>"><?php echo e($tag->name); ?></a><?php if(!$loop->last): ?>,<?php endif; ?></li>
                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-lg-12">
                  <div class="text-center py-5">
                    <i class="fa fa-file-text-o fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">No Posts Found</h4>
                    <p class="text-muted">There are no blog posts available at the moment.</p>
                  </div>
                </div>
                <?php endif; ?>

                <!-- Laravel Pagination -->
                <?php if($posts->hasPages()): ?>
                <div class="col-lg-12">
                  <div class="d-flex justify-content-center">
                    <?php echo e($posts->appends(request()->query())->links()); ?>

                  </div>
                </div>
                <?php endif; ?>
              </div>
            </div>
          </div>
          <div class="col-lg-4">
            <div class="sidebar">
              <div class="row">
                <div class="col-lg-12">
                  <div class="sidebar-item search">
                    <div class="website-search-container">
                      <input type="text" class="searchText website-search" placeholder="Search posts..." autocomplete="off">
                    </div>
                    <form id="search_form" name="gs" method="GET" action="<?php echo e(route('blog')); ?>">
                      <input type="text" name="search" class="searchText" placeholder="Search posts..."
                             value="<?php echo e(request('search')); ?>" autocomplete="on">
                      <!-- Preserve other filters -->
                      <?php if(request('category')): ?>
                        <input type="hidden" name="category" value="<?php echo e(request('category')); ?>">
                      <?php endif; ?>
                      <?php if(request('tag')): ?>
                        <input type="hidden" name="tag" value="<?php echo e(request('tag')); ?>">
                      <?php endif; ?>
                    </form>
                  </div>
                </div>

                <!-- Current Filters -->
                <?php if(request('search') || request('category') || request('tag')): ?>
                <div class="col-lg-12">
                  <div class="sidebar-item">
                    <div class="sidebar-heading">
                      <h2>Active Filters</h2>
                    </div>
                    <div class="content">
                      <div class="filter-tags">
                        <?php if(request('search')): ?>
                          <span class="filter-tag">
                            Search: "<?php echo e(request('search')); ?>"
                            <a href="<?php echo e(route('blog', array_filter(request()->except('search')))); ?>" class="remove-filter">×</a>
                          </span>
                        <?php endif; ?>
                        <?php if(request('category')): ?>
                          <span class="filter-tag">
                            Category: <?php echo e(ucfirst(request('category'))); ?>

                            <a href="<?php echo e(route('blog', array_filter(request()->except('category')))); ?>" class="remove-filter">×</a>
                          </span>
                        <?php endif; ?>
                        <?php if(request('tag')): ?>
                          <span class="filter-tag">
                            Tag: <?php echo e(ucfirst(request('tag'))); ?>

                            <a href="<?php echo e(route('blog', array_filter(request()->except('tag')))); ?>" class="remove-filter">×</a>
                          </span>
                        <?php endif; ?>
                        <a href="<?php echo e(route('blog')); ?>" class="clear-all-filters">Clear All</a>
                      </div>
                    </div>
                  </div>
                </div>
                <?php endif; ?>

                <div class="col-lg-12">
                  <div class="sidebar-item recent-posts">
                    <div class="sidebar-heading">
                      <h2>Recent Posts</h2>
                    </div>
                    <div class="content">
                      <ul>
                        <?php $__empty_1 = true; $__currentLoopData = $recent_posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recent_post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <li><a href="<?php echo e(route('post-detail', $recent_post->slug)); ?>">
                          <h5><?php echo e(Str::limit($recent_post->title, 50)); ?></h5>
                          <span><?php echo e($recent_post->published_at ? $recent_post->published_at->format('M d, Y') : $recent_post->created_at->format('M d, Y')); ?></span>
                        </a></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <li><a href="#">
                          <h5>No recent posts available</h5>
                          <span><?php echo e(date('M d, Y')); ?></span>
                        </a></li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item categories">
                    <div class="sidebar-heading">
                      <h2>Categories</h2>
                    </div>
                    <div class="content">
                      <ul>
                        <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <li>
                          <a href="<?php echo e(route('blog', ['category' => $category->slug])); ?>"
                             class="<?php echo e(request('category') == $category->slug ? 'active' : ''); ?>">
                            - <?php echo e($category->name); ?> (<?php echo e($category->posts_count); ?>)
                          </a>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <li><a href="#">- No categories available</a></li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-lg-12">
                  <div class="sidebar-item tags">
                    <div class="sidebar-heading">
                      <h2>Tag Clouds</h2>
                    </div>
                    <div class="content">
                      <ul>
                        <?php $__empty_1 = true; $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <li>
                          <a href="<?php echo e(route('blog', ['tag' => $tag->slug])); ?>"
                             class="<?php echo e(request('tag') == $tag->slug ? 'active' : ''); ?>">
                            <?php echo e($tag->name); ?>

                          </a>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <li><a href="#">No tags available</a></li>
                        <?php endif; ?>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('website.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/website/blog.blade.php ENDPATH**/ ?>