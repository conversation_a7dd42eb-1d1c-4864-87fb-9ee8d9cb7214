<?php $__env->startSection('title', 'Edit Post'); ?>

<?php $__env->startSection('content'); ?>
<div class="app-title">
  <div>
    <h1><i class="bi bi-pencil"></i> Edit Post</h1>
    <p>Update blog post: <?php echo e($post->title); ?></p>
  </div>
  <ul class="app-breadcrumb breadcrumb">
    <li class="breadcrumb-item"><i class="bi bi-house-door fs-6"></i></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('posts.index')); ?>">Posts</a></li>
    <li class="breadcrumb-item active">Edit</li>
  </ul>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="tile">
      <div class="tile-body">
        <form action="<?php echo e(route('posts.update', $post)); ?>" method="POST" enctype="multipart/form-data">
          <?php echo csrf_field(); ?>
          <?php echo method_field('PUT'); ?>
          
          <div class="row">
            <div class="col-md-8">
              <!-- Title -->
              <div class="mb-3">
                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="title" name="title" value="<?php echo e(old('title', $post->title)); ?>" required>
                <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>

              <!-- Excerpt -->
              <div class="mb-3">
                <label for="excerpt" class="form-label">Excerpt</label>
                <textarea class="form-control <?php $__errorArgs = ['excerpt'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                          id="excerpt" name="excerpt" rows="3" placeholder="Brief description of the post"><?php echo e(old('excerpt', $post->excerpt)); ?></textarea>
                <?php $__errorArgs = ['excerpt'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>

              <!-- Content -->
              <div class="mb-3">
                <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                <textarea class="form-control <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                          id="content" name="content" rows="15" required><?php echo e(old('content', $post->content)); ?></textarea>
                <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                  <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
              </div>

              <!-- SEO Section -->
              <div class="card mb-3">
                <div class="card-header">
                  <h5 class="card-title mb-0">SEO Settings</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label for="meta_title" class="form-label">Meta Title</label>
                    <input type="text" class="form-control <?php $__errorArgs = ['meta_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           id="meta_title" name="meta_title" value="<?php echo e(old('meta_title', $post->meta_title)); ?>">
                    <?php $__errorArgs = ['meta_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                      <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                  </div>
                  <div class="mb-3">
                    <label for="meta_description" class="form-label">Meta Description</label>
                    <textarea class="form-control <?php $__errorArgs = ['meta_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                              id="meta_description" name="meta_description" rows="3"><?php echo e(old('meta_description', $post->meta_description)); ?></textarea>
                    <?php $__errorArgs = ['meta_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                      <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <!-- Publish Settings -->
              <div class="card mb-3">
                <div class="card-header">
                  <h5 class="card-title mb-0">Publish Settings</h5>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" id="status" name="status" value="1" 
                             <?php echo e(old('status', $post->status) ? 'checked' : ''); ?>>
                      <label class="form-check-label" for="status">
                        Published
                      </label>
                    </div>
                  </div>
                  <div class="mb-3">
                    <label for="published_at" class="form-label">Publish Date</label>
                    <input type="datetime-local" class="form-control <?php $__errorArgs = ['published_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           id="published_at" name="published_at" 
                           value="<?php echo e(old('published_at', $post->published_at ? $post->published_at->format('Y-m-d\TH:i') : '')); ?>">
                    <?php $__errorArgs = ['published_at'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                      <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                  </div>
                </div>
              </div>

              <!-- Category -->
              <div class="card mb-3">
                <div class="card-header">
                  <h5 class="card-title mb-0">Category</h5>
                </div>
                <div class="card-body">
                  <select class="form-select <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                          id="category_id" name="category_id" required>
                    <option value="">Select Category</option>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <option value="<?php echo e($category->id); ?>"
                              <?php echo e(old('category_id', $post->category_id) == $category->id ? 'selected' : ''); ?>>
                        <?php echo e($category->name); ?>

                      </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </select>
                  <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
              </div>

              <!-- Tags -->
              <div class="card mb-3">
                <div class="card-header">
                  <h5 class="card-title mb-0">Tags</h5>
                </div>
                <div class="card-body">
                  <select class="form-select <?php $__errorArgs = ['tags'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                          id="tags" name="tags[]" multiple="multiple">
                    <?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <option value="<?php echo e($tag->id); ?>"
                              data-color="<?php echo e($tag->color); ?>"
                              <?php echo e(in_array($tag->id, old('tags', $post->tags->pluck('id')->toArray())) ? 'selected' : ''); ?>>
                        <?php echo e($tag->name); ?>

                      </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </select>
                  <?php $__errorArgs = ['tags'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                  <small class="form-text text-muted">
                    <i class="bi bi-info-circle"></i> Select multiple tags. Colors will be displayed automatically.
                  </small>
                </div>
              </div>

              <!-- Featured Image -->
              <div class="card mb-3">
                <div class="card-header">
                  <h5 class="card-title mb-0">Featured Image</h5>
                </div>
                <div class="card-body">
                  <?php if($post->featured_image): ?>
                    <div class="mb-3">
                      <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" alt="Current featured image" 
                           class="img-fluid rounded" style="max-height: 200px;">
                      <p class="text-muted mt-2">Current featured image</p>
                    </div>
                  <?php endif; ?>
                  <input type="file" class="form-control <?php $__errorArgs = ['featured_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                         id="featured_image" name="featured_image" accept="image/*">
                  <?php $__errorArgs = ['featured_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                  <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                  <small class="form-text text-muted">Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</small>
                </div>
              </div>

              <!-- Post Stats -->
              <div class="card mb-3">
                <div class="card-header">
                  <h5 class="card-title mb-0">Post Statistics</h5>
                </div>
                <div class="card-body">
                  <p><strong>Views:</strong> <?php echo e($post->views_count); ?></p>
                  <p><strong>Created:</strong> <?php echo e($post->created_at->format('M d, Y H:i')); ?></p>
                  <p><strong>Last Updated:</strong> <?php echo e($post->updated_at->format('M d, Y H:i')); ?></p>
                  <?php if($post->published_at): ?>
                    <p><strong>Published:</strong> <?php echo e($post->published_at->format('M d, Y H:i')); ?></p>
                  <?php endif; ?>
                </div>
              </div>
            </div>
          </div>

          <div class="tile-footer">
            <div class="d-flex justify-content-between">
              <a href="<?php echo e(route('posts.index')); ?>" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Posts
              </a>
              <div>
                <a href="<?php echo e(route('posts.show', $post)); ?>" class="btn btn-info me-2">
                  <i class="bi bi-eye"></i> View Post
                </a>
                <button type="submit" class="btn btn-primary">
                  <i class="bi bi-check-circle"></i> Update Post
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
  // Initialize CKEditor 4 (More stable and reliable)
  document.addEventListener('DOMContentLoaded', function() {
    // Check if CKEditor is loaded
    if (typeof CKEDITOR === 'undefined') {
      console.error('CKEditor not loaded');
      return;
    }

    // Suppress CKEditor version warnings and notifications
    CKEDITOR.config.versionCheck = false;

    // Override CKEDITOR.error to suppress specific errors
    const originalCKError = CKEDITOR.error;
    CKEDITOR.error = function(errorCode, additionalData) {
      // Suppress plugin-required errors for notification
      if (errorCode === 'editor-plugin-required' &&
          additionalData &&
          additionalData.plugin === 'notification') {
        console.log('CKEditor notification plugin requirement suppressed');
        return;
      }
      // Call original error for other cases
      originalCKError.call(this, errorCode, additionalData);
    };

    // Override console methods to suppress warnings
    const originalWarn = console.warn;
    console.warn = function(...args) {
      const message = args.join(' ');
      if (!message.includes('CKEditor') && !message.includes('version is not secure')) {
        originalWarn.apply(console, args);
      }
    };

    // Initialize CKEditor 4.25.1 LTS Full Build with all features
    CKEDITOR.replace('content', {
      height: 400,
      toolbar: [
        { name: 'document', items: ['Source', '-', 'NewPage', 'Preview'] },
        { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
        '/',
        { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
        { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },
        { name: 'links', items: ['Link', 'Unlink', 'Anchor'] },
        { name: 'insert', items: ['Image', 'Table', 'HorizontalRule', 'SpecialChar'] },
        '/',
        { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
        { name: 'colors', items: ['TextColor', 'BGColor'] },
        { name: 'tools', items: ['Maximize', 'ShowBlocks'] }
      ],
      // Enhanced configuration
      format_tags: 'p;h1;h2;h3;h4;h5;h6;pre;address;div',
      fontSize_sizes: '11/11px;12/12px;13/13px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',
      font_names: 'Arial/Arial, Helvetica, sans-serif;Times New Roman/Times New Roman, Times, serif;Courier New/Courier New, Courier, monospace;Georgia/Georgia, serif;Verdana/Verdana, Geneva, sans-serif;Trebuchet MS/Trebuchet MS, Helvetica, sans-serif;Tahoma/Tahoma, Geneva, sans-serif;Impact/Impact, Charcoal, sans-serif;Comic Sans MS/Comic Sans MS, cursive',

      // Content styling
      contentsCss: [
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css'
      ],

      // Styles set
      stylesSet: [
        { name: 'Paragraph', element: 'p' },
        { name: 'Heading 1', element: 'h1' },
        { name: 'Heading 2', element: 'h2' },
        { name: 'Heading 3', element: 'h3' },
        { name: 'Heading 4', element: 'h4' },
        { name: 'Blue Title', element: 'h3', styles: { 'color': 'Blue' } },
        { name: 'Red Title', element: 'h3', styles: { 'color': 'Red' } },
        { name: 'Marker: Yellow', element: 'span', styles: { 'background-color': 'Yellow' } },
        { name: 'Marker: Green', element: 'span', styles: { 'background-color': 'Lime' } },
        { name: 'Big', element: 'big' },
        { name: 'Small', element: 'small' },
        { name: 'Computer Code', element: 'code' },
        { name: 'Keyboard Phrase', element: 'kbd' },
        { name: 'Sample Text', element: 'samp' },
        { name: 'Variable', element: 'var' }
      ],

      // Basic configuration
      enterMode: CKEDITOR.ENTER_P,
      shiftEnterMode: CKEDITOR.ENTER_BR,
      autoParagraph: false,
      fillEmptyBlocks: false,

      // Minimal plugin removal to avoid conflicts
      removePlugins: 'exportpdf,easyimage,cloudservices,about',

      // Only remove buttons we don't need
      removeButtons: 'Save,NewPage,Preview,Print,Templates,Find,Replace,SelectAll,Scayt,Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,CopyFormatting,CreateDiv,BidiLtr,BidiRtl,Language,Flash,Smiley,PageBreak,Iframe,About',

      // Disable version check and warnings
      versionCheck: false,
      disableNativeSpellChecker: false,

      // Event handlers to suppress warnings
      on: {
        instanceReady: function(evt) {
          console.log('CKEditor instance ready - warnings suppressed');
        }
      },

      // Dialog settings
      removeDialogTabs: 'image:advanced;link:advanced;table:advanced'
    });

    // Store reference for form submission
    window.editor = CKEDITOR.instances.content;
    console.log('CKEditor 4 initialized successfully');
  });

  // Initialize Select2 for Categories
  $('#category_id').select2({
    theme: 'bootstrap-5',
    placeholder: 'Select Category',
    allowClear: true,
    width: '100%',
    dropdownParent: $('#category_id').parent()
  });

  // Initialize Select2 for Tags with custom formatting
  $('#tags').select2({
    theme: 'bootstrap-5',
    placeholder: 'Select Tags',
    allowClear: true,
    width: '100%',
    dropdownParent: $('#tags').parent(),
    templateResult: formatTag,
    templateSelection: formatTagSelection,
    closeOnSelect: false
  });

  // Custom formatting for tags in dropdown
  function formatTag(tag) {
    if (!tag.id) {
      return tag.text;
    }

    const $tag = $(tag.element);
    const color = $tag.data('color') || '#007bff';

    const $result = $(
      '<div class="select2-tag-option">' +
        '<span class="tag-color-indicator" style="background-color: ' + color + ';"></span>' +
        '<span class="tag-text">' + tag.text + '</span>' +
      '</div>'
    );

    return $result;
  }

  // Custom formatting for selected tags
  function formatTagSelection(tag) {
    if (!tag.id) {
      return tag.text;
    }

    const $tag = $(tag.element);
    const color = $tag.data('color') || '#007bff';

    return $('<span class="selected-tag" style="background-color: ' + color + '; color: white; padding: 3px 8px; border-radius: 15px; font-size: 11px; margin: 2px;">' + tag.text + '</span>');
  }

  // Preview featured image
  document.getElementById('featured_image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        // Add image preview logic here if needed
      };
      reader.readAsDataURL(file);
    }
  });

  // Form validation before submit
  $('form').on('submit', function(e) {
    // Update CKEditor content to textarea before submit
    if (window.editor && window.editor.getData) {
      document.querySelector('#content').value = window.editor.getData();
    }
  });

  // Add some visual feedback when tags are selected
  $('#tags').on('select2:select', function (e) {
    const data = e.params.data;
    console.log('Tag selected:', data.text);

    // Add a subtle animation to the selection
    setTimeout(function() {
      $('.select2-selection__choice').last().addClass('newly-selected');
      setTimeout(function() {
        $('.newly-selected').removeClass('newly-selected');
      }, 1000);
    }, 100);
  });

  // Handle tag removal
  $('#tags').on('select2:unselect', function (e) {
    const data = e.params.data;
    console.log('Tag removed:', data.text);
  });
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('css'); ?>
<style>
/* Select2 Custom Styling */
.select2-container--bootstrap-5 .select2-selection {
  border: 1px solid #ced4da;
  border-radius: 8px;
  min-height: 38px;
  padding: 4px 8px;
}

.select2-container--bootstrap-5 .select2-selection--single {
  height: 38px;
  line-height: 38px;
}

.select2-container--bootstrap-5 .select2-selection--multiple {
  min-height: 38px;
  padding: 2px 8px;
}

.select2-container--bootstrap-5 .select2-selection__rendered {
  color: #495057;
  padding-left: 0;
  padding-right: 0;
}

.select2-container--bootstrap-5 .select2-selection__placeholder {
  color: #6c757d;
}

.select2-container--bootstrap-5 .select2-selection__arrow {
  height: 36px;
  right: 8px;
}

.select2-container--bootstrap-5 .select2-selection__clear {
  color: #6c757d;
  font-size: 18px;
  margin-right: 8px;
}

/* Dropdown styling */
.select2-container--bootstrap-5 .select2-dropdown {
  border: 1px solid #ced4da;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.select2-container--bootstrap-5 .select2-results__option {
  padding: 8px 12px;
  border-bottom: 1px solid #f8f9fa;
}

.select2-container--bootstrap-5 .select2-results__option--highlighted {
  background-color: #007bff;
  color: white;
}

/* Tag option styling */
.select2-tag-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tag-color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.tag-text {
  font-weight: 500;
}

/* Selected tags styling */
.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 15px;
  color: #495057;
  font-size: 12px;
  margin: 2px;
  padding: 2px 8px;
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove {
  color: #6c757d;
  margin-right: 5px;
  font-weight: bold;
}

.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #dc3545;
}

/* Focus states */
.select2-container--bootstrap-5.select2-container--focus .select2-selection {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Search input */
.select2-container--bootstrap-5 .select2-search--inline .select2-search__field {
  border: none;
  outline: none;
  font-size: 14px;
  margin: 0;
  padding: 4px 0;
}

/* Multiple selection container */
.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__rendered {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  padding: 2px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .select2-container--bootstrap-5 .select2-selection {
    min-height: 42px;
  }

  .select2-container--bootstrap-5 .select2-selection--single {
    height: 42px;
    line-height: 42px;
  }
}

/* Animation for dropdown */
.select2-container--bootstrap-5 .select2-dropdown {
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom tag colors for selected items */
.selected-tag {
  display: inline-block !important;
  white-space: nowrap;
  font-weight: 600;
  text-shadow: 0 1px 1px rgba(0,0,0,0.2);
}

/* Animation for newly selected tags */
.select2-selection__choice.newly-selected {
  animation: tagPulse 0.6s ease;
  transform: scale(1.05);
}

@keyframes tagPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(13, 110, 253, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
  }
}

/* Improved hover effects */
.select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  transition: all 0.2s ease;
}

/* Better focus indication */
.select2-container--bootstrap-5.select2-container--focus .select2-selection--multiple {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  transition: all 0.2s ease;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/dashboard/posts/edit.blade.php ENDPATH**/ ?>