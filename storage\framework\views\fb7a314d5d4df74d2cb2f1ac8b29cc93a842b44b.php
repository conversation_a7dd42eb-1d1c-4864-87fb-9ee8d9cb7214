<!DOCTYPE html>
<html lang="en">

  <head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="TemplateMo">
    <link href="https://fonts.googleapis.com/css?family=Roboto:100,100i,300,300i,400,400i,500,500i,700,700i,900,900i&display=swap" rel="stylesheet">

    <title>blog website</title>
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/owl.css')); ?>">
    <!-- Owl Carousel CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css"/>




    <!-- Bootstrap core CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">

    <!-- Additional CSS Files -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/templatemo-stand-blog.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/owl.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('css/ajax-search.css')); ?>">

    <!-- Custom Website CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/website/custom.css')); ?>">

  </head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">

   
<!-- 
        <?php if (! empty(trim($__env->yieldContent('header')))): ?>
            <header class="bg-white shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <?php echo $__env->yieldContent('header'); ?>
                </div>
            </header>
        <?php endif; ?> -->

             <?php echo $__env->make('website.template.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <main>
            <?php echo $__env->yieldContent('content'); ?>
        </main>
         <?php echo $__env->make('website.template.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>

   <!-- Bootstrap core JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Owl Carousel JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>

    <!-- Additional Scripts -->
    <script src="<?php echo e(asset('assets/js/owl.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/slick.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/isotope.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/accordions.js')); ?>"></script>

    <script language = "text/Javascript"> 
      cleared[0] = cleared[1] = cleared[2] = 0; //set a cleared flag for each field
      function clearField(t){                   //declaring the array outside of the
      if(! cleared[t.id]){                      // function makes it static and global
          cleared[t.id] = 1;  // you could use true and false, but that's more typing
          t.value='';         // with more chance of typos
          t.style.color='#fff';
          }
      }
    </script>
<script>
  $(document).ready(function(){
    $(".owl-carousel").owlCarousel({
      items: 3,
      loop: true,
      margin: 10,
      nav: true,
      autoplay: true,
      autoplayTimeout: 3000,
      autoplayHoverPause: true
    });
  });
</script>

<!-- jQuery Validation Plugin -->
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>

<!-- Custom Website JavaScript -->
<script src="<?php echo e(asset('js/website/main.js')); ?>"></script>
<script src="<?php echo e(asset('js/ajax-search.js')); ?>"></script>
<script src="<?php echo e(asset('js/comment-validation.js')); ?>"></script>

  </body>
</html><?php /**PATH C:\xampp\htdocs\blog-website\resources\views/website/layouts/master.blade.php ENDPATH**/ ?>